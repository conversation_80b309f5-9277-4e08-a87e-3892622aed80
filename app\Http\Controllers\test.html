<html class="scroll-smooth" lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   Cosmic Creation Animation
  </title>
  <script src="https://cdn.tailwindcss.com">
  </script>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;600&amp;display=swap" rel="stylesheet"/>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet"/>
  <style>
   body {
      font-family: "Poppins", sans-serif;
      background-color: #0a0a23;
      color: #f9f7f1;
      overflow: hidden;
      margin: 0;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      perspective: 1500px;
    }
    .container {
      position: relative;
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      background: radial-gradient(
        ellipse at center,
        #0a0a23 0%,
        #000000 80%
      );
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .slide {
      position: absolute;
      width: 100vw;
      height: 100vh;
      top: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 1rem 2rem;
      text-align: center;
      opacity: 0;
      transform-style: preserve-3d;
      backface-visibility: hidden;
      color: #f9f7f1;
      user-select: none;
    }
    .slide.active {
      opacity: 1;
      z-index: 10;
      animation: fadeInUp 1.5s ease forwards;
    }
    .slide img {
      max-width: 320px;
      max-height: 320px;
      border-radius: 1.5rem;
      box-shadow: 0 0 30px #ffd700cc;
      margin-bottom: 1.5rem;
      object-fit: contain;
      filter: drop-shadow(0 0 10px #ffd700);
      animation: pulseLight 3s ease-in-out infinite;
    }
    h1,
    h2,
    h3 {
      margin: 0;
      font-weight: 600;
      text-shadow: 0 0 10px #ffd700cc;
    }
    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      color: #fff9d6;
    }
    h2 {
      font-size: 2.25rem;
      margin-bottom: 0.75rem;
      color: #ffec99;
    }
    h3 {
      font-size: 1.75rem;
      margin-bottom: 0.5rem;
      color: #ffec99;
    }
    p {
      font-size: 1.15rem;
      max-width: 600px;
      line-height: 1.5;
      color: #ffec99cc;
      text-shadow: 0 0 5px #ffd700aa;
      margin: 0 auto;
    }
    /* Animations */
    @keyframes pulseLight {
      0%,
      100% {
        filter: drop-shadow(0 0 15px #fff9d6);
        opacity: 0.85;
      }
      50% {
        filter: drop-shadow(0 0 35px #fff9d6);
        opacity: 1;
      }
    }
    @keyframes fadeInUp {
      0% {
        opacity: 0;
        transform: translateY(40px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }
    /* Spiral container */
    .spiral-container {
      position: relative;
      width: 480px;
      height: 480px;
      margin: 0 auto 2rem;
      animation: slowRotate 60s linear infinite;
      filter: drop-shadow(0 0 20px #ffd700cc);
    }
    @keyframes slowRotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    /* Spiral images */
    .spiral-img {
      position: absolute;
      border-radius: 9999px;
      box-shadow: 0 0 15px #ffd700cc;
      object-fit: contain;
      filter: drop-shadow(0 0 10px #ffd700);
      background: radial-gradient(circle, #ffd70044 0%, transparent 70%);
    }
    /* Positions for spiral elements */
    .spiral-nirakar {
      width: 96px;
      height: 96px;
      top: 8px;
      left: 50%;
      transform: translateX(-50%);
      animation: pulseLight 3s ease-in-out infinite;
    }
    .spiral-shivshakti {
      width: 72px;
      height: 72px;
      top: 120px;
      left: 25%;
      transform: translateX(-50%);
      animation: pulseLight 3.5s ease-in-out infinite;
    }
    .spiral-vishnu {
      width: 64px;
      height: 64px;
      top: 220px;
      right: 25%;
      animation: pulseLight 4s ease-in-out infinite;
    }
    .spiral-brahma {
      width: 56px;
      height: 56px;
      bottom: 160px;
      left: 33%;
      transform: translateX(-50%);
      animation: pulseLight 4.5s ease-in-out infinite;
    }
    .spiral-farishtas {
      width: 48px;
      height: 48px;
      bottom: 120px;
      right: 50%;
      transform: translateX(50%);
      animation: pulseLight 5s ease-in-out infinite;
    }
    .spiral-aatmas {
      width: 40px;
      height: 40px;
      bottom: 80px;
      left: 16%;
      transform: translateX(-50%);
      animation: pulseLight 5.5s ease-in-out infinite;
    }
    .spiral-sapta {
      width: 36px;
      height: 36px;
      bottom: 48px;
      right: 33%;
      animation: pulseLight 6s ease-in-out infinite;
    }
    .spiral-prajapati {
      width: 32px;
      height: 32px;
      bottom: 32px;
      left: 50%;
      transform: translateX(-50%);
      animation: pulseLight 6.5s ease-in-out infinite;
    }
    .spiral-devtas {
      width: 28px;
      height: 28px;
      bottom: 20px;
      right: 25%;
      animation: pulseLight 7s ease-in-out infinite;
    }
    .spiral-humans {
      width: 24px;
      height: 24px;
      bottom: 0;
      left: 33%;
      transform: translateX(-50%);
      animation: pulseLight 7.5s ease-in-out infinite;
    }
    /* Responsive */
    @media (max-width: 768px) {
      .spiral-container {
        width: 320px;
        height: 320px;
      }
      .spiral-nirakar {
        width: 64px;
        height: 64px;
        top: 6px;
      }
      .spiral-shivshakti {
        width: 48px;
        height: 48px;
        top: 80px;
        left: 25%;
      }
      .spiral-vishnu {
        width: 48px;
        height: 48px;
        top: 140px;
        right: 25%;
      }
      .spiral-brahma {
        width: 40px;
        height: 40px;
        bottom: 100px;
        left: 33%;
      }
      .spiral-farishtas {
        width: 36px;
        height: 36px;
        bottom: 80px;
        right: 50%;
      }
      .spiral-aatmas {
        width: 32px;
        height: 32px;
        bottom: 56px;
        left: 16%;
      }
      .spiral-sapta {
        width: 28px;
        height: 28px;
        bottom: 32px;
        right: 33%;
      }
      .spiral-prajapati {
        width: 24px;
        height: 24px;
        bottom: 20px;
        left: 50%;
      }
      .spiral-devtas {
        width: 20px;
        height: 20px;
        bottom: 12px;
        right: 25%;
      }
      .spiral-humans {
        width: 18px;
        height: 18px;
        bottom: 0;
        left: 33%;
      }
    }
  </style>
 </head>
 <body>
  <main aria-atomic="true" aria-live="polite" class="container" role="main">
   <!-- Slides container -->
   <section class="relative w-full h-full" id="slides-wrapper">
    <!-- Slide 0: Infinite Void -->
    <article aria-label="Infinite cosmic void with subtle stars" class="slide active" data-index="0">
     <img alt="Vast infinite cosmic void with subtle twinkling stars scattered across a deep cosmic black and violet background" height="320" loading="eager" src="https://storage.googleapis.com/a1aa/image/85f21763-2b82-4d2f-5c95-b1a97ea12c43.jpg" style="filter: drop-shadow(0 0 10px #fff); border-radius: 50%;" width="320"/>
     <h1>
      Begin with a vast, infinite void — a cosmic silence.
     </h1>
    </article>
    <!-- Slide 1: Nirakar Shiva -->
    <article aria-label="Nirakar Shiva glowing divine light" class="slide" data-index="1">
     <img alt="Nirakar Shiva, a formless glowing divine light in golden-white luminosity, softly glowing and floating in timeless cosmic space" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/0cbbcb32-d06c-4439-e6ac-2ca6de095853.jpg" width="320"/>
     <h2>
      Nirakar Shiva
     </h2>
     <p>
      An eternal, boundless divine light appears — Nirakar Shiva, glowing softly in golden-white luminosity, formless yet deeply peaceful, floating in timeless space.
     </p>
    </article>
    <!-- Slide 2: Shakar Shiva and Shakti -->
    <article aria-label="Shakar Shiva and Shakti divine figures" class="slide" data-index="2">
     <img alt="Shakar Shiva, a radiant majestic figure formed from divine golden light, serene and majestic, standing tall with cosmic energy" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/30e6f437-e554-4d24-d7d2-b22761dcf87e.jpg" style="margin-bottom: 1rem;" width="320"/>
     <img alt="Shakti, a glowing powerful feminine force of energy, radiant with golden and amber light, swirling and vibrant" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/fab9ceed-0949-4d29-506c-d002817e74cd.jpg" width="320"/>
     <h2>
      Shakar Shiva &amp; Shakti
     </h2>
     <p>
      They unite in harmony — forming ShivShakti, a cosmic duality in one form, radiating both power and balance.
     </p>
    </article>
    <!-- Slide 3: ShivShakti -->
    <article aria-label="ShivShakti cosmic duality" class="slide" data-index="3">
     <img alt="ShivShakti, a cosmic duality figure combining Shakar Shiva and Shakti, radiating golden light and balance, majestic and serene" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/bbefb9e3-9c01-4d62-e3f8-28e8e44b74c9.jpg" width="320"/>
     <h2>
      ShivShakti — Cosmic Duality
     </h2>
     <p>
      Radiating both power and balance, the union of Shiva and Shakti manifests cosmic harmony.
     </p>
    </article>
    <!-- Slide 4: Vishnu -->
    <article aria-label="Vishnu resting on coiled serpent" class="slide" data-index="4">
     <img alt="Vishnu, celestial blue figure with peaceful smile, resting on a coiled serpent, symbolizing balance and sustenance, glowing softly" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/3ce2322c-faa0-4eb4-6520-c59ef988cfa7.jpg" width="320"/>
     <h2>
      Vishnu — Balance and Sustenance
     </h2>
     <p>
      With a divine glance, Vishnu creates Brahma, seated on a lotus blooming from his navel.
     </p>
    </article>
    <!-- Slide 5: Brahma -->
    <article aria-label="Brahma radiant being with four heads on lotus" class="slide" data-index="5">
     <img alt="Brahma, radiant being with four heads, seated on a blooming lotus flower, glowing with creative power and divine light" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/82a46071-de2c-4e1a-831d-666ac049e52a.jpg" width="320"/>
     <h2>
      Brahma — The Creator
     </h2>
     <p>
      Infused with creative power, Brahma begins his task by first creating Farishtas (angels) and high-quality Aatmas (souls), then manifests Prajapati, the powerful progenitor emitting divine knowledge and willpower.
     </p>
    </article>
    <!-- Slide 6: Farishtas and Aatmas -->
    <article aria-label="Farishtas angels and Aatmas souls descending" class="slide" data-index="6">
     <img alt="Farishtas, transparent glowing angels of light, hovering in the sky with soft blue and white luminescence" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/ebd0dd3c-2457-4c62-7d61-5c9757089404.jpg" style="margin-bottom: 1rem;" width="320"/>
     <img alt="Aatmas, shining brilliant stars representing souls, descending gently into a divine realm with golden-white glow" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/eb6a5414-cb58-4a77-b64c-638f9e2dcd4b.jpg" width="320"/>
     <h2>
      Farishtas &amp; Aatmas
     </h2>
     <p>
      Transparent, glowing angels and brilliant souls descending gently into the divine realm.
     </p>
    </article>
    <!-- Slide 7: Prajapati -->
    <article aria-label="Prajapati powerful progenitor" class="slide" data-index="7">
     <img alt="Prajapati, powerful progenitor emitting divine knowledge and willpower, glowing golden figure with radiant aura" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/42739393-4d1e-4015-a1d3-7d3910131fe9.jpg" width="320"/>
     <h2>
      Prajapati — Progenitor
     </h2>
     <p>
      Emitting divine knowledge and willpower, the powerful progenitor who continues creation.
     </p>
    </article>
    <!-- Slide 8: Sapta Rishis -->
    <article aria-label="Sapta Rishis seven glowing sages in meditation" class="slide" data-index="8">
     <div style="display:flex;gap:0.5rem;justify-content:center;flex-wrap:wrap;max-width:600px;margin-bottom:1rem;">
      <img alt="Sapta Rishi 1, glowing sage with pink aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/c6df15fa-26e7-4f49-bfeb-4d8ca9967cf2.jpg" width="80"/>
      <img alt="Sapta Rishi 2, glowing sage with aqua aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/7a43bc2d-818e-496c-9ea0-2182b7372f48.jpg" width="80"/>
      <img alt="Sapta Rishi 3, glowing sage with violet aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/6abff991-8b26-41fa-b716-200812bacaa8.jpg" width="80"/>
      <img alt="Sapta Rishi 4, glowing sage with golden aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/a56cb33c-4919-45a5-5c1a-e5de870afe53.jpg" width="80"/>
      <img alt="Sapta Rishi 5, glowing sage with sky blue aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/ede0e13b-4678-45a8-b47c-2202d290ba7c.jpg" width="80"/>
      <img alt="Sapta Rishi 6, glowing sage with coral aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/9efb9688-9093-414c-f979-6e15d36b7d76.jpg" width="80"/>
      <img alt="Sapta Rishi 7, glowing sage with light green aura, seated in deep meditation surrounded by celestial energy" height="80" loading="lazy" src="https://storage.googleapis.com/a1aa/image/5ff7a7de-eeaa-4bfa-b2d1-bde70d20c5d2.jpg" width="80"/>
     </div>
     <h2>
      Sapta Rishis — Seven Glowing Sages
     </h2>
     <p>
      Seven glowing sages, each with unique colored auras, seated in deep meditation surrounded by celestial energy.
     </p>
    </article>
    <!-- Slide 9: Devtas Slideshow -->
    <article aria-label="Devtas celestial beings representing elements" class="slide" data-index="9">
     <div aria-atomic="true" aria-live="polite" class="slideshow-container" role="region" style="max-width: 700px; margin: 0 auto;">
      <div class="slide-devtas active" data-index="0" style="text-align:center; color:#ffda44;">
       <img alt="Agni, Fire Deity, celestial being glowing with intense orange and red flames" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/8ea89d6d-06f9-4b50-1e62-96b3909a3ede.jpg" style="border-radius:1rem; box-shadow:0 0 20px #ff4500;" width="400"/>
       <h3>
        Agni (Fire)
       </h3>
       <p>
        Celestial being glowing with intense orange and red flames, representing the element of fire.
       </p>
      </div>
      <div class="slide-devtas" data-index="1" style="text-align:center; color:#a0d8ef;">
       <img alt="Vayu, Air Deity, celestial being glowing with soft white and grey swirling winds" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/b4ab0d83-de8a-4d97-d232-88c4deb19c32.jpg" style="border-radius:1rem; box-shadow:0 0 20px #a0d8ef;" width="400"/>
       <h3>
        Vayu (Air)
       </h3>
       <p>
        Celestial being glowing with soft white and grey swirling winds, representing the element of air.
       </p>
      </div>
      <div class="slide-devtas" data-index="2" style="text-align:center; color:#1e40af;">
       <img alt="Dyaus, Sky Deity, celestial being glowing with deep blue and cosmic sky hues" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/b05e3fc4-ab49-4352-d830-a141a6b80301.jpg" style="border-radius:1rem; box-shadow:0 0 20px #1e40af;" width="400"/>
       <h3>
        Dyaus (Sky)
       </h3>
       <p>
        Celestial being glowing with deep blue and cosmic sky hues, representing the element of sky.
       </p>
      </div>
      <div class="slide-devtas" data-index="3" style="text-align:center; color:#4d7c0f;">
       <img alt="Prithvi, Earth Deity, celestial being glowing with deep green and brown tones" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/2989d363-49de-4668-d0cd-c18dea894669.jpg" style="border-radius:1rem; box-shadow:0 0 20px #4d7c0f;" width="400"/>
       <h3>
        Prithvi (Earth)
       </h3>
       <p>
        Celestial being glowing with deep green and brown tones, representing the element of earth.
       </p>
      </div>
      <div class="slide-devtas" data-index="4" style="text-align:center; color:#2563eb;">
       <img alt="Varuna, Water Deity, celestial being glowing with deep blue and aqua waves" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/ed29aa2d-7608-4e94-458e-392c94970aec.jpg" style="border-radius:1rem; box-shadow:0 0 20px #2563eb;" width="400"/>
       <h3>
        Varuna (Water)
       </h3>
       <p>
        Celestial being glowing with deep blue and aqua waves, representing the element of water.
       </p>
      </div>
      <div class="slide-devtas" data-index="5" style="text-align:center; color:#fbbf24;">
       <img alt="Surya, Sun Deity, celestial being glowing with bright golden sun rays" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/738b1fdd-a09f-4c8e-0779-3734a2fc32dc.jpg" style="border-radius:1rem; box-shadow:0 0 20px #fbbf24;" width="400"/>
       <h3>
        Surya (Sun)
       </h3>
       <p>
        Celestial being glowing with bright golden sun rays, representing the sun.
       </p>
      </div>
      <div class="slide-devtas" data-index="6" style="text-align:center; color:#93c5fd;">
       <img alt="Chandra, Moon Deity, celestial being glowing with soft silver and blue moonlight" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/4fb2d617-82f2-47a3-84fd-d1211db5aaed.jpg" style="border-radius:1rem; box-shadow:0 0 20px #93c5fd;" width="400"/>
       <h3>
        Chandra (Moon)
       </h3>
       <p>
        Celestial being glowing with soft silver and blue moonlight, representing the moon.
       </p>
      </div>
      <div class="slide-devtas" data-index="7" style="text-align:center; color:#fcd34d;">
       <img alt="Nakshatra, Stars Deity, celestial being glowing with twinkling star lights" height="240" loading="lazy" src="https://storage.googleapis.com/a1aa/image/611f2a1e-a8a1-4f9d-7b3f-d3a62daa5a69.jpg" style="border-radius:1rem; box-shadow:0 0 20px #fcd34d;" width="400"/>
       <h3>
        Nakshatra (Stars)
       </h3>
       <p>
        Celestial being glowing with twinkling star lights, representing the stars.
       </p>
      </div>
     </div>
    </article>
    <!-- Slide 10: Humans -->
    <article aria-label="Humans placed gently on Earth in harmony with nature" class="slide" data-index="10">
     <img alt="Humans, fragile yet divine beings, placed gently on Earth surrounded by lush green nature, trees, rivers, and mountains, symbolizing harmony and conscious evolution" height="320" loading="lazy" src="https://storage.googleapis.com/a1aa/image/886eff4d-739f-46f3-73d9-17f664fb0a57.jpg" style="border-radius: 1.5rem; box-shadow: 0 0 30px #7cfc00cc;" width="320"/>
     <h2>
      Humans — Divine Beings Beginning Conscious Evolution
     </h2>
     <p>
      Fragile yet divine, placed gently on Earth in harmony with nature.
     </p>
    </article>
    <!-- Slide 11: Golden Spiral Chain -->
    <article aria-label="Golden spiral chain of creation from humans to Nirakar Shiva" class="slide" data-index="11">
     <div aria-hidden="true" aria-label="Golden spiral of light ascending from humans to Nirakar Shiva" class="spiral-container" tabindex="-1">
      <img alt="Golden spiral of light ascending from humans to Nirakar Shiva, showing the entire chain of creation in a glowing spiral pattern" aria-hidden="true" class="spiral-img spiral-spiral" height="480" loading="lazy" src="https://storage.googleapis.com/a1aa/image/92ed76bf-b8b4-4cfa-29f7-82990017d0ed.jpg" style="width: 480px; height: 480px; border-radius: 50%;" width="480"/>
      <img alt="Nirakar Shiva glowing golden-white light at the top of the spiral" class="spiral-img spiral-nirakar" height="96" loading="lazy" src="https://storage.googleapis.com/a1aa/image/276b6864-4391-4064-ba54-6a380125c7b2.jpg" width="96"/>
      <img alt="ShivShakti cosmic duality figure glowing golden light in the spiral" class="spiral-img spiral-shivshakti" height="72" loading="lazy" src="https://storage.googleapis.com/a1aa/image/97e99f6a-73a2-4f8e-ec37-5de01050d12d.jpg" width="72"/>
      <img alt="Vishnu celestial blue figure resting on serpent in the spiral" class="spiral-img spiral-vishnu" height="64" loading="lazy" src="https://storage.googleapis.com/a1aa/image/f0748e28-db2b-4e98-b17e-b04f6bb117c4.jpg" width="64"/>
      <img alt="Brahma radiant being with four heads on lotus in the spiral" class="spiral-img spiral-brahma" height="56" loading="lazy" src="https://storage.googleapis.com/a1aa/image/061310e4-adb2-4aa3-c0ac-8e1261a4bce2.jpg" width="56"/>
      <img alt="Farishtas transparent glowing angels of light in the spiral" class="spiral-img spiral-farishtas" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/028f4be6-e5ac-45cc-6e64-fc50cccea575.jpg" width="48"/>
      <img alt="Aatmas shining brilliant stars souls in the spiral" class="spiral-img spiral-aatmas" height="40" loading="lazy" src="https://storage.googleapis.com/a1aa/image/001366c3-07e8-425e-a305-2d08647633ee.jpg" width="40"/>
      <img alt="Sapta Rishis seven glowing sages in meditation in the spiral" class="spiral-img spiral-sapta" height="36" loading="lazy" src="https://storage.googleapis.com/a1aa/image/456d9e58-06aa-466a-7627-c580a4f7de29.jpg" width="36"/>
      <img alt="Prajapati powerful progenitor glowing golden in the spiral" class="spiral-img spiral-prajapati" height="32" loading="lazy" src="https://storage.googleapis.com/a1aa/image/38801a6f-43da-4f29-9a2e-4379cc974f4e.jpg" width="32"/>
      <img alt="Devtas celestial beings representing elements in the spiral" class="spiral-img spiral-devtas" height="28" loading="lazy" src="https://storage.googleapis.com/a1aa/image/febd7a9e-1fe8-40eb-4c61-3408e7cfed06.jpg" width="28"/>
      <img alt="Humans fragile yet divine beings on Earth in the spiral" class="spiral-img spiral-humans" height="24" loading="lazy" src="https://storage.googleapis.com/a1aa/image/73146bd6-956a-4b3a-3d71-9109eeec5d19.jpg" width="24"/>
     </div>
     <h2>
      The Chain of Creation — A Golden Spiral of Divine Light
     </h2>
     <p>
      From humans to Nirakar Shiva, the eternal cosmic cycle of creation ascends in harmony and light.
     </p>
    </article>
   </section>
  </main>
  <!-- Sacred Geometry and Symbols Background -->
  <img alt="Background of sacred geometry patterns glowing softly in violet and gold, including lotus flowers, trishul, conch shells, spiral galaxies, third eye symbols, OM signs, and yantras" aria-hidden="true" class="fixed inset-0 w-full h-full object-cover opacity-20 pointer-events-none -z-10" height="1080" loading="eager" src="https://storage.googleapis.com/a1aa/image/ce4a9533-3ae7-4048-5ff5-b51d3839d59b.jpg" width="1920"/>
  <!-- Ambient celestial music embed (muted by default) -->
  <audio autoplay="" class="hidden" id="ambient-music" loop="" muted="">
   <source src="https://cdn.pixabay.com/download/audio/2022/03/15/audio_0a7a3a3a3a.mp3?filename=ambient-celestial-11207.mp3" type="audio/mpeg"/>
  </audio>
  <script>
   // Ambient music unmute on first user interaction
    document.body.addEventListener(
      "click",
      () => {
        const audio = document.getElementById("ambient-music");
        if (audio && audio.muted) {
          audio.muted = false;
          audio.volume = 0.15;
        }
      },
      { once: true }
    );

    // Slide animation logic
    const slides = document.querySelectorAll(".slide");
    let currentSlide = 0;
    const slideDuration = 7000; // 7 seconds per slide

    function showSlide(index) {
      slides.forEach((slide, i) => {
        slide.classList.toggle("active", i === index);
      });
    }

    function nextSlide() {
      currentSlide = (currentSlide + 1) % slides.length;
      showSlide(currentSlide);
    }

    // Devtas slideshow inside slide 9
    const devtasSlides = document.querySelectorAll(".slide-devtas");
    let devtasCurrent = 0;
    const devtasDuration = 4000;

    function showDevtasSlide(index) {
      devtasSlides.forEach((slide, i) => {
        slide.style.opacity = i === index ? "1" : "0";
        slide.style.position = i === index ? "relative" : "absolute";
        slide.style.pointerEvents = i === index ? "auto" : "none";
        slide.style.transition = "opacity 1s ease-in-out";
      });
    }

    function nextDevtasSlide() {
      devtasCurrent = (devtasCurrent + 1) % devtasSlides.length;
      showDevtasSlide(devtasCurrent);
    }

    // Initialize
    showSlide(currentSlide);
    showDevtasSlide(devtasCurrent);

    // Slide interval
    setInterval(nextSlide, slideDuration);
    // Devtas interval
    setInterval(nextDevtasSlide, devtasDuration);
  </script>
 </body>
</html>
