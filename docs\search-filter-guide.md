# Search and Filter Guide

This guide explains how to use the search and filter functionality in the ProductController.

## Basic Usage

### Simple Pagination
```
GET /api/products?page=1&per_page=5
```

### Category Filter (Equals)
```
GET /api/products?filter[category]=Toys&filter_type[category]=equals
```

### Name Filter (Contains)
```
GET /api/products?filter[name]=dddd&filter_type[name]=contains
```

### Multiple Filters
```
GET /api/products?page=1&per_page=5&filter[category]=Toys&filter_type[category]=equals&filter[name]=dddd&filter_type[name]=contains
```

## Available Filter Types

### String Filters
- `equals` - Exact match
- `contains` - Contains substring (default for text fields)
- `starts_with` - Starts with substring
- `ends_with` - Ends with substring
- `not_equals` - Not equal to

### Numeric Filters
- `greater_than` - Greater than value
- `less_than` - Less than value
- `greater_than_or_equal` - Greater than or equal to value
- `less_than_or_equal` - Less than or equal to value

### Array Filters
- `in` - Value is in comma-separated list
- `not_in` - Value is not in comma-separated list

### Special Filters
- `between` - Between two values (for ranges)
- `null` - Field is null
- `not_null` - Field is not null

## Examples

### Price Range Filter
```
GET /api/products?filter[price]=100&filter_type[price]=greater_than
GET /api/products?filter[price]=500&filter_type[price]=less_than
```

### Multiple Categories
```
GET /api/products?filter[category]=Toys,Electronics&filter_type[category]=in
```

### Global Search
```
GET /api/products?search=laptop
```

### Sorting
```
GET /api/products?sort=price&direction=desc
GET /api/products?sort=name&direction=asc
```

### Combined Example
```
GET /api/products?page=1&per_page=10&filter[category]=Electronics&filter_type[category]=equals&filter[price]=100&filter_type[price]=greater_than&sort=price&direction=asc&search=laptop
```

## Response Format

The API returns paginated results in Laravel's standard pagination format:

```json
{
  "current_page": 1,
  "data": [
    {
      "id": 1,
      "name": "Product Name",
      "category": "Electronics",
      "price": 299.99,
      "stock": 50,
      "status": "active",
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    }
  ],
  "first_page_url": "http://localhost/api/products?page=1",
  "from": 1,
  "last_page": 5,
  "last_page_url": "http://localhost/api/products?page=5",
  "links": [...],
  "next_page_url": "http://localhost/api/products?page=2",
  "path": "http://localhost/api/products",
  "per_page": 5,
  "prev_page_url": null,
  "to": 5,
  "total": 25
}
```

## Available Filter Fields

- `name` - Product name
- `category` - Product category
- `price` - Product price
- `stock` - Stock quantity
- `status` - Product status
- `description` - Product description

## Getting Filter Options

To get available categories and statuses for dropdowns:

```
GET /api/products/filter-options
```

Response:
```json
{
  "categories": ["Electronics", "Toys", "Clothing"],
  "statuses": ["active", "inactive", "out_of_stock"],
  "filter_types": {
    "equals": "Equals",
    "contains": "Contains",
    ...
  }
}
```
