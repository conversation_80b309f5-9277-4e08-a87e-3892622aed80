<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class ProductController extends Controller
{
    // public function index(Request $request)
    // {

    //     //return in form of api response
    //     //check if page is provied case for limit and page
    //     $limit = $request->input('limit', 5);
    //     $products = Product::paginate($limit);
    //     return response()->json($products);

    //     // Check if this is an AJAX request from DataTables
    //     // if ($request->ajax()) {
    //     //     $products = Product::query();

    //     //     return DataTables::of($products)
    //     //         // ->addColumn('image', function ($product) {
    //     //         //     return '<img src="'.$product->image_url.'" alt="'.$product->name.'" class="product-image">';
    //     //         // })
    //     //         ->addColumn('status', function ($product) {
    //     //             return '<span class="status-badge status-'.$product->status.'">'.
    //     //                 ucfirst(str_replace('_', ' ', $product->status)).'</span>';
    //     //         })
    //     //         ->rawColumns(['status'])
    //     //         ->make(true);
    //     // }

    //     // // For non-AJAX requests, return the view
    //     // return view('product-index');
    // }
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 5); // Default to 5 if not provided
        $page = $request->get('page', 1);

        $query = Product::query();

        // Handle individual filter parameters
        $this->applyFilters($query, $request);

        // Handle sorting
        if ($request->has('sort')) {
            $sortField = $request->get('sort');
            $sortDirection = $request->get('direction', 'asc');
            $query->orderBy($sortField, $sortDirection);
        }

        // Paginate with the requested per_page value
        $result = $query->paginate($perPage, ['*'], 'page', $page);

        // Return JSON response for API requests
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json($result);
        }

        return $result;
    }

    /**
     * Apply filters to the query based on request parameters
     */
    private function applyFilters($query, Request $request)
    {
        // Handle category filter
        if ($request->has('filter') && is_array($request->get('filter'))) {
            foreach ($request->get('filter') as $field => $value) {
                if (!empty($value)) {
                    $filterType = $request->get("filter_type.{$field}", 'contains');
                    $this->applyFieldFilter($query, $field, $value, $filterType);
                }
            }
        }

        // Handle individual filter parameters (like filter[category])
        if ($request->has('filter[category]')) {
            $categoryValue = $request->get('filter[category]');
            $categoryFilterType = $request->get('filter_type[category]', 'equals');
            if (!empty($categoryValue)) {
                $this->applyFieldFilter($query, 'category', $categoryValue, $categoryFilterType);
            }
        }

        // Handle name filter
        if ($request->has('filter[name]')) {
            $nameValue = $request->get('filter[name]');
            $nameFilterType = $request->get('filter_type[name]', 'contains');
            if (!empty($nameValue)) {
                $this->applyFieldFilter($query, 'name', $nameValue, $nameFilterType);
            }
        }

        // Handle other potential filters
        $filterableFields = ['price', 'stock', 'status', 'description'];
        foreach ($filterableFields as $field) {
            if ($request->has("filter[{$field}]")) {
                $value = $request->get("filter[{$field}]");
                $filterType = $request->get("filter_type[{$field}]", 'contains');
                if (!empty($value)) {
                    $this->applyFieldFilter($query, $field, $value, $filterType);
                }
            }
        }

        // Handle global search (if you have a search parameter)
        if ($request->has('search') && !empty($request->get('search'))) {
            $searchTerm = $request->get('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('category', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }
    }

    /**
     * Apply a specific filter to a field based on filter type
     */
    private function applyFieldFilter($query, $field, $value, $filterType)
    {
        switch ($filterType) {
            case 'equals':
                $query->where($field, '=', $value);
                break;
            case 'contains':
                $query->where($field, 'like', "%{$value}%");
                break;
            case 'starts_with':
                $query->where($field, 'like', "{$value}%");
                break;
            case 'ends_with':
                $query->where($field, 'like', "%{$value}");
                break;
            case 'not_equals':
                $query->where($field, '!=', $value);
                break;
            case 'greater_than':
                $query->where($field, '>', $value);
                break;
            case 'less_than':
                $query->where($field, '<', $value);
                break;
            case 'greater_than_or_equal':
                $query->where($field, '>=', $value);
                break;
            case 'less_than_or_equal':
                $query->where($field, '<=', $value);
                break;
            case 'in':
                // Handle comma-separated values
                $values = is_array($value) ? $value : explode(',', $value);
                $query->whereIn($field, $values);
                break;
            case 'not_in':
                // Handle comma-separated values
                $values = is_array($value) ? $value : explode(',', $value);
                $query->whereNotIn($field, $values);
                break;
            case 'between':
                // Expecting value to be an array with min and max
                if (is_array($value) && count($value) === 2) {
                    $query->whereBetween($field, $value);
                }
                break;
            case 'null':
                $query->whereNull($field);
                break;
            case 'not_null':
                $query->whereNotNull($field);
                break;
            default:
                // Default to 'contains' behavior
                $query->where($field, 'like', "%{$value}%");
                break;
        }
    }
}

