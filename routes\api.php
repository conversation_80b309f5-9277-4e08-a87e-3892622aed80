<?php

use App\Http\Controllers\ProductController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Test CORS endpoint
Route::get('/test-cors', function () {
    return response()->json([
        'message' => 'CORS is working!',
        'timestamp' => now(),
        'origin' => request()->header('Origin'),
        'method' => request()->method()
    ]);
});

// Public API routes (no authentication required)
Route::get('/products', [ProductController::class, 'index']);

// Test route to show available filter options
Route::get('/products/filter-options', function () {
    return response()->json([
        'categories' => \App\Models\Product::distinct()->pluck('category')->filter(),
        'statuses' => \App\Models\Product::distinct()->pluck('status')->filter(),
        'filter_types' => [
            'equals' => 'Equals',
            'contains' => 'Contains',
            'starts_with' => 'Starts with',
            'ends_with' => 'Ends with',
            'not_equals' => 'Not equals',
            'greater_than' => 'Greater than',
            'less_than' => 'Less than',
            'greater_than_or_equal' => 'Greater than or equal',
            'less_than_or_equal' => 'Less than or equal',
            'in' => 'In (comma separated)',
            'not_in' => 'Not in (comma separated)',
            'between' => 'Between',
            'null' => 'Is null',
            'not_null' => 'Is not null'
        ]
    ]);
});

// You can add more API routes here
// Example authenticated routes:
// Route::middleware('auth:sanctum')->group(function () {
//     Route::post('/products', [ProductController::class, 'store']);
//     Route::put('/products/{product}', [ProductController::class, 'update']);
//     Route::delete('/products/{product}', [ProductController::class, 'destroy']);
// });
